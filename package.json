{"private": true, "author": "王明远 <wangmingyuan>", "scripts": {"dev": "umi dev", "build": "umi build", "postinstall": "umi setup", "setup": "umi setup", "start": "npm run dev"}, "dependencies": {"@tailwindcss/postcss": "^4.1.11", "@turf/helpers": "^7.2.0", "@turf/turf": "^7.2.0", "@types/geojson": "^7946.0.16", "ahooks": "^3.9.0", "antd": "^5.26.5", "dayjs": "^1.11.13", "echarts": "^6.0.0", "echarts-for-react": "^3.0.2", "es-toolkit": "^1.39.9", "geojson": "^0.5.0", "mapvgl": "1.0.0-beta.194", "nanoid": "^5.1.5", "react-bmapgl": "^1.0.1", "react-custom-scrollbars": "^4.2.1", "react-if": "^4.1.6", "tailwindcss": "^4.1.11", "three": "^0.179.1", "umi": "^4.4.11", "unstated-next": "^1.1.0", "zustand": "^5.0.6"}, "devDependencies": {"@types/bmapgl": "^0.0.7", "@types/react": "^18.0.33", "@types/react-custom-scrollbars": "^4.0.13", "@types/react-dom": "^18.0.11", "@types/three": "^0.179.0", "@umijs/lint": "^4.4.11", "@umijs/plugins": "^4.4.11", "eslint": "^8.57.1", "stylelint": "^14", "tailwindcss": "^3", "typescript": "^5.0.3"}}