import { defineConfig } from "umi";

const isDev = process.env.NODE_ENV === "development";

export default defineConfig({
  plugins: ["@umijs/plugins/dist/tailwindcss", "@umijs/plugins/dist/request"],
  base: isDev ? undefined : "/base/",
  publicPath: isDev ? undefined : "/base/",
  routes: [
    // {
    //   path: "/",
    //   redirect: "/userManage",
    // },
    { path: "/", component: "Home" },
    { path: "/satellite", component: "Satellite" },
    { path: "/subway", component: "Subway" },
  ],

  monorepoRedirect: {},
  request: {
    dataField: "data",
  },
  proxy: {
    "/api": {
      target: "http://*************:19120",
      changeOrigin: true,
      // 支持https
      // secure: true,
      pathRewrite: { "^/api": "" },
    },
  },
  headScripts: [
    "https://api.map.baidu.com/api?type=webgl&v=1.0&ak=1XjLLEhZhQNUzd93EjU5nOGQ",
    "https://api.map.baidu.com/api?type=subway&v=1.0&ak=1XjLLEhZhQNUzd93EjU5nOGQ",
  ],
  history: { type: "browser" },
  npmClient: "pnpm",
  // tailwindcss: {},
  extraPostCSSPlugins: [require("@tailwindcss/postcss")],
});
