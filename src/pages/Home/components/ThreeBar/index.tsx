import React, { useCallback, useRef } from "react";
import { MapvglLayer } from "react-bmapgl";
// mapvgl闭源，没有导出类型包，所以需要使用@ts-ignore
// @ts-ignore
import * as mapvgl from 'mapvgl';
import * as THREE from "three";
import { PROVINCIAL_CAPITALS } from "@/constant";
import { useMount } from "ahooks";

export interface IProps {
  height?: number;
}

function ThreeBar(props: IProps) {
  const { height = 2 } = props;
  const threeLayerRef = useRef<any>(null);

  // 坐标转换
  const convertLL2MC = useCallback((point: BMapGL.Point) => {
    if (!mapvgl || !mapvgl.MercatorProjection) {
      console.warn("mapvgl not ready yet");
      return null;
    }
    return mapvgl.MercatorProjection.convertLL2MC(point);
  }, []);

  const create3DCylinders = useCallback(() => {
    const cylinders: THREE.Mesh[] = [];

    PROVINCIAL_CAPITALS.forEach((capital) => {
      const geometry = new THREE.CylinderGeometry(
        height,
        height,
        height * 10,
        8
      );

      const material = new THREE.MeshPhongMaterial({
        color: "#2C5264",
        transparent: true,
        opacity: 1,
      });

      const cylinder = new THREE.Mesh(geometry, material);

      // 坐标转换
      const point = new BMapGL.Point(capital.lng, capital.lat);
      const mercatorPoint = convertLL2MC(point);

      if (!mercatorPoint) {
        console.warn(`Failed to convert coordinates for ${capital.name}`);
        return;
      }

      // cylinder.position.set(mercatorPoint.lng, mercatorPoint.lat, height / 2);
      cylinder.position.setZ(height / 2);
      cylinder.userData = {
        name: capital.name,
        province: capital.province,
        height: height,
        originalY: cylinder.position.y,
      };
      // add第二个参数影响到元素是否跟随地图缩放而缩放，类似于threehtml中的精灵属性
      threeLayerRef?.current?.add(cylinder, mercatorPoint);
      cylinders.push(cylinder);
    });

    // 添加光照以增强视觉效果
    const ambientLight = new THREE.AmbientLight("#ffffff", 1); // 环境光
    threeLayerRef.current.add(ambientLight);
  }, [convertLL2MC, height]);

  useMount(() => {
    create3DCylinders();
  });

  return (
    <React.Fragment>
      {/* @ts-ignore */}
      <MapvglLayer
        type="ThreeLayer"
        options={{ enablePicked: true }}
        ref={(layer) => {
          threeLayerRef.current = layer?.layer;
        }}
      />
    </React.Fragment>
  );
}

export default ThreeBar;
