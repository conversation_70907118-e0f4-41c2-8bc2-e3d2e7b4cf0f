import React, { useMemo } from "react";
import chinaJson from "@/assets/china.json";
import { Polygon } from "react-bmapgl";
// import type { Geometry } from "geojson";
import { Else, If, Then } from "react-if";
// import {centroid} from "@turf/turf";

interface ProvinceData {
  adcode: string;
  name: string;
  percentage: number;
  center: [number, number];
  geometry: any;
}

const PolygonConfig = {
  strokeColor: "#fff",
  strokeWeight: 2,
  fillColor: "#0073F7",
  fillOpacity: 0.3,
};

function PolygonComponent() {
  const mockProvinceData = useMemo(() => {
    const data: ProvinceData[] = [];
    chinaJson.features.forEach((feature: any) => {
      if (feature.properties.level === "province") {
        // 生成更分散的百分比数据，确保颜色差异明显
        let percentage: number;
        const rand = Math.random();
        if (rand < 0.15) {
          percentage = Math.floor(Math.random() * 6) + 70; // 70-75% (最深蓝色)
        } else if (rand < 0.3) {
          percentage = Math.floor(Math.random() * 5) + 76; // 76-80%
        } else if (rand < 0.45) {
          percentage = Math.floor(Math.random() * 5) + 81; // 81-85%
        } else if (rand < 0.6) {
          percentage = Math.floor(Math.random() * 5) + 86; // 86-90%
        } else if (rand < 0.8) {
          percentage = Math.floor(Math.random() * 4) + 91; // 91-94%
        } else {
          percentage = Math.floor(Math.random() * 5) + 95; // 95-99%
        }
        data.push({
          adcode: feature.properties.adcode,
          name: feature.properties.name,
          percentage: percentage,
          center: feature.properties.center,
          geometry: feature.geometry,
        });
      }
    });
    return data;
  }, []);

  return (
    <React.Fragment>
      {mockProvinceData.map((item, index) => {
        const { geometry } = item;
        return (
          <React.Fragment key={index}>
            <If condition={geometry.type === "Polygon"}>
              <Then>
                {geometry.coordinates.map((cItem: any, cIndex: number) => {
                  const points = cItem.map(
                    (coord: any) => new BMapGL.Point(coord[0], coord[1])
                  );
                  return (
                    // @ts-ignore
                    <Polygon
                      key={cIndex}
                      path={points}
                      {...PolygonConfig}
                      onMouseover={(e) => {
                        console.log(e);
                      }}
                    />
                  );
                })}
              </Then>
              <Else>
                {geometry.type === "MultiPolygon" && (
                  <React.Fragment>
                    {geometry.coordinates.map((cItem: any, cIndex: number) => {
                      return (
                        <React.Fragment key={cIndex}>
                          {cItem.map((dItem: any, dIndex: number) => {
                            const points = dItem.map(
                              (coord: any) =>
                                new BMapGL.Point(coord[0], coord[1])
                            );
                            return (
                              // @ts-ignore
                              <Polygon
                                key={dIndex}
                                path={points}
                                {...PolygonConfig}
                              />
                            );
                          })}
                        </React.Fragment>
                      );
                    })}
                  </React.Fragment>
                )}
              </Else>
            </If>
          </React.Fragment>
        );
      })}
    </React.Fragment>
  );
}

export default PolygonComponent;
