import { useEffect, useRef } from "react";
import { Map, MapvglView } from "react-bmapgl";
import mapJson from "@/assets/map.json";
import ThreeBar from "./components/ThreeBar";
import PolygonComponent from "./components/Polygon";

function HomePage() {
  const mapRef = useRef<BMapGL.Map | undefined>(undefined);

  useEffect(() => {
    mapRef.current?.setMapStyleV2({
      styleJson: mapJson,
    });
  }, []);

  return (
    <div className="w-full h-full">
      {/* @ts-ignore */}
      <Map
        ref={(value) => {
          mapRef.current = value?.map;
        }}
        // mapType="satellite"
        style={{ height: "100vh" }}
        center={new BMapGL.Point(116.404449, 39.914889)}
        zoom={6} // 调整缩放级别以看到更多城市
        heading={0}
        tilt={45} // 增加倾斜角度以更好地展示3D效果
        onClick={(e) => console.log(e)}
        enableScrollWheelZoom
      >
        {/* @ts-ignore */}
        <MapvglView effects={["bright"]}>
          <ThreeBar height={5} />
          <PolygonComponent />
        </MapvglView>
      </Map>
    </div>
  );
}
export default HomePage;
