import React from "react";

interface IProps {
  title: string;
  children: React.ReactNode;
  className?: string;
}

function BaseCard(props: IProps) {
  return (
    <React.Fragment>
      <div
        className={`${props.className} flex flex-col bg-[rgba(42,65,96,0.46)] shadow-[0_6px_10px_0_rgba(0,0,0,0.06)]`}
      >
        <div className="absolute w-[15px] h-[118px] bg-[#5FEAFF] left-0 top-0"></div>
        <div className="h-[118px] w-full text-[56px] font-bold px-[75px] py-[16px] flex items-center text-[#fff] bg-[#324355]">
          {props.title}
        </div>
        {props.children}
      </div>
    </React.Fragment>
  );
}

export default BaseCard;
