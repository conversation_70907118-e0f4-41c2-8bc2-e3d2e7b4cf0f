import { useMount, useUnmount } from "ahooks";
import { useRef } from "react";
import styles from "./index.module.less";

function Subway() {
  const mapRef = useRef<HTMLDivElement>(null);
  const subwayRef = useRef<any | null>(null);

  useMount(() => {
    // 延迟初始化，等待ReactScaleScreen缩放完成
    const timer = setTimeout(() => {
      if (!window.BMapSub || !window.BMapSub.SubwayCitiesList) {
        console.error("请确认已引入百度地铁图API的JS和CSS");
        return;
      }

      const subwayCityName = "北京";
      const targetCity = window.BMapSub.SubwayCitiesList.find(
        (city: any) => city.name === subwayCityName
      );

      if (!targetCity) {
        console.error(`城市${subwayCityName}不在支持列表中`);
        return;
      }
      if (!mapRef.current) {
        return;
      }

      try {
        const container = mapRef.current;

        // 清空容器
        container.innerHTML = '';

        // 获取容器的实际渲染尺寸
        const rect = container.getBoundingClientRect();
        console.log('地铁图容器尺寸:', rect.width, 'x', rect.height);

        // 关键修复：强制设置容器的clientWidth和clientHeight
        // 这样百度地铁图SDK就能获取到正确的尺寸
        const realWidth = Math.floor(rect.width);
        const realHeight = Math.floor(rect.height);

        Object.defineProperty(container, 'clientWidth', {
          value: realWidth,
          writable: false,
          configurable: true
        });
        Object.defineProperty(container, 'clientHeight', {
          value: realHeight,
          writable: false,
          configurable: true
        });

        // 同时修复getBoundingClientRect方法
        const originalGetBoundingClientRect = container.getBoundingClientRect;
        container.getBoundingClientRect = function() {
          const originalRect = originalGetBoundingClientRect.call(this);
          return {
            ...originalRect,
            width: realWidth,
            height: realHeight
          };
        };

        // 创建地铁图实例
        const subwayInstance = new window.BMapSub.Subway(
          container,
          targetCity.citycode
        );
        subwayRef.current = subwayInstance;

        // 设置地铁图参数
        subwayInstance.setZoom(1);
        subwayInstance.setCenter("安定门");

        console.log('地铁图初始化成功，修正后容器尺寸:', container.clientWidth, 'x', container.clientHeight);
      } catch (err) {
        console.error("初始化错误：", err);
      }
    }, 1000); // 增加延迟时间确保缩放完成

    return () => clearTimeout(timer);
  });

  useUnmount(() => {
    // 清理地铁图实例
    if (subwayRef.current) {
      subwayRef.current = null;
    }

    // 清空DOM容器
    if (mapRef.current) {
      mapRef.current.innerHTML = '';
    }
  });

  return (
    <div className={styles.subContainer}>
      <div className={styles.subMain} ref={mapRef}></div>
    </div>
  );
}

export default Subway;
