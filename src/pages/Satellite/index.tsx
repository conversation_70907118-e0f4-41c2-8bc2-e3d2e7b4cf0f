import React, { useEffect, useRef } from "react";
import { Map, MapvglView } from "react-bmapgl";
import mapJson from "@/assets/map.json";

function Satellite() {
  const mapRef = useRef<BMapGL.Map | undefined>(undefined);

  useEffect(() => {
    // mapRef.current?.setMapStyleV2({
    //   styleJson: mapJson,
    // });
  }, []);
  
  return (
    <React.Fragment>
      <div className="size-full">
        {/* @ts-ignore */}
        <Map
          ref={(value) => {
            mapRef.current = value?.map;
          }}
          mapType="earth"
          style={{ height: "100%" }}
          center={new BMapGL.Point(116.404449, 39.914889)}
          zoom={6} // 调整缩放级别以看到更多城市
          heading={0}
          onClick={(e) => console.log(e)}
          enableScrollWheelZoom
        >
          {/* @ts-ignore */}
          {/* <MapvglView effects={["bright"]}>
            
          </MapvglView> */}
        </Map>
      </div>
    </React.Fragment>
  );
}

export default Satellite;
