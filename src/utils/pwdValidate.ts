import CryptoJS from "crypto-js";

export const pwdMessage: Record<string, string> = {
  length: "口令长度应大于等于8个字符，不超过16个字符",
  containingType:
    "口令应由大写字母、小写字母、数字、特殊符号四种类型组成；（特殊符号举例如下：!@#$%^&*()_+|~-=`{}[]:”;’<>?,./）",
  sameCheck: "不得使用一串相同的数字或字母作为口令",
  noHasUserName: "口令中不能含有账号",
};

const lengthValidator = (_: any, value: string) => {
  if (!value || /^.{8,16}$/.test(value)) {
    return Promise.resolve();
  }
  return Promise.reject(pwdMessage.length);
};

const containingTypeValidator = (_: any, value: string) => {
  if (
    // eslint-disable-next-line no-useless-escape
    !value ||
    /^(?=.*[A-Z])(?=.*[a-z])(?=.*\d)(?=.*[!@#$%^&*()_+\-|~=`{}\\[\]:\\";'<>?,.\\/])([A-Za-z\d!@#$%^&*()_+\-|~=`{}\\[\]:\\";'<>?,.\\/])+$/.test(
      value
    )
  ) {
    return Promise.resolve();
  }
  return Promise.reject(pwdMessage.containingType);
};

const sameValidator = (_: any, value: string) => {
  if (!value) {
    return Promise.resolve();
  }
  for (let i = 0; i < value.length - 2; i++) {
    if (
      (/[a-zA-Z]/.test(value[i]) &&
        value[i] === value[i + 1] &&
        value[i] === value[i + 2]) ||
      (/\d/.test(value[i]) &&
        value[i] === value[i + 1] &&
        value[i] === value[i + 2])
    ) {
      return Promise.reject(pwdMessage.sameCheck);
    }
  }
  return Promise.resolve();
};

const noHasUserNameValidator = (loginId?: string) => {
  return (_: any, value: string) => {
    if (loginId && value.includes(loginId)) {
      return Promise.reject(pwdMessage.noHasUserName);
    }
    return Promise.resolve();
  };
};

export const getPwdRules = (loginId?: string) => {
  return [
    {
      validator: lengthValidator,
    },
    {
      validator: containingTypeValidator,
    },
    {
      validator: sameValidator,
    },
    {
      validator: noHasUserNameValidator(loginId),
    },
  ];
};

export const encrypt = (message: string, key = "uaop-controller") => {
  const keyHex = CryptoJS.enc.Utf8.parse(key);
  const encrypted = CryptoJS.DES.encrypt(message, keyHex, {
    mode: CryptoJS.mode.ECB,
    padding: CryptoJS.pad.Pkcs7,
  });
  return encrypted.toString();
};
