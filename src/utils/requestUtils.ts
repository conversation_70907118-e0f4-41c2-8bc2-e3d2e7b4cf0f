import type { RequestOptions } from "umi";
import qs from "qs";
import { md5 } from "js-md5";
import useStore from "@/store";

export const getRequestBody = (config: RequestOptions) => {
  const { method, data, params } = config;
  let body = "";
  if (method === "get" || method === "delete") {
    // get和delete 取消encode 编码 防止出现和后台签名错误问题
    body = qs.stringify(params, { encode: false });
  } else {
    body = data ? JSON.stringify(data) : "";
  }
  return body;
};

export const getHeaders = (body: string) => {
  const { loginInfo, tenantId } = useStore.getState();
  const { ssoUserId: userId, secret } = loginInfo! ?? {};
  const headers = {
    tenantId,
    userId,
    Authorization: "{}",
  };

  if (secret) {
    const timestamp = +new Date();
    const onceStr = Math.random().toString(36).substr(2);
    let str = "";
    try {
      str =
        userId +
        tenantId +
        timestamp +
        onceStr +
        secret +
        decodeURIComponent(encodeURIComponent(body));
    } catch (error) {
      str = userId + tenantId + timestamp + onceStr + secret + body;
    }
    const token = md5(str).toLowerCase();
    headers.Authorization = JSON.stringify({
      userId,
      tenantId,
      timestamp,
      onceStr,
      token,
    });
  }
  return headers;
};
