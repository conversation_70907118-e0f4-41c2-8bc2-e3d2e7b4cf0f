import { createJSONStorage, persist } from "zustand/middleware";
import { immer } from "zustand/middleware/immer";
import { shallow } from "zustand/shallow";
import { createWithEqualityFn } from "zustand/traditional";

interface GlobalState {
  token: string;
}

const useStore = createWithEqualityFn<GlobalState>()(
  persist(
    immer((set, get) => ({
      token: "",
    })),
    {
      name: "zw-storage",
      storage: createJSONStorage(() => localStorage),
    }
  ),
  shallow
);

export default useStore;
