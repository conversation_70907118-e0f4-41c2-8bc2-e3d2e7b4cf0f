import type { ThemeConfig } from "antd";
import { ConfigProvider } from "antd";
import zhCN from "antd/locale/zh_CN";
import dayjs from "dayjs";
import "dayjs/locale/zh-cn";
import { useMemo } from "react";
import { Outlet } from "umi";
dayjs.locale("zh-cn");

export default function Layout() {
  const themeConfig = useMemo(() => {
    const value: ThemeConfig = {
      token: {
        colorPrimary: "#3b64fc",
      },
    };
    return value;
  }, []);

  return (
    <ConfigProvider
      locale={zhCN}
      wave={{
        disabled: true, // 禁用 wave
      }}
      theme={themeConfig}
    >
      <div className="w-screen h-screen relative">
        <Outlet />
      </div>
      {/* <ReactScaleScreen width={1920} height={1080}> */}

      {/* </ReactScaleScreen> */}
    </ConfigProvider>
  );
}
